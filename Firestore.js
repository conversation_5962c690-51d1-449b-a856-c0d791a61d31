// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
/* eslint @typescript-eslint/no-unused-vars: ["error", { "varsIgnorePattern": "^getFirestore$" }] */
/**
 * An authenticated interface to a Firestore project.
 */
class Firestore {
    /**
     * Constructor
     *
     * @param {string} accessToken The access token for authentication
     * @param {string} projectId the Firestore project ID
     * @param {string} apiVersion [Optional] The Firestore API Version ("v1beta1", "v1beta2", or "v1")
     * @return {Firestore} an authenticated interface with a Firestore project (constructor)
     */
    constructor(accessToken, projectId, apiVersion = 'v1') {
        this.get_ = FirestoreRead.prototype.get_;
        this.getPage_ = FirestoreRead.prototype.getPage_;
        this.getDocumentResponsesFromCollection_ = FirestoreRead.prototype.getDocumentResponsesFromCollection_;
        this.getDocument_ = FirestoreRead.prototype.getDocument_;
        this.getDocuments_ = FirestoreRead.prototype.getDocuments_;
        this.getDocumentIds_ = FirestoreRead.prototype.getDocumentIds_;
        this.createDocument_ = FirestoreWrite.prototype.createDocument_;
        this.updateDocument_ = FirestoreWrite.prototype.updateDocument_;
        this.deleteDocument_ = FirestoreDelete.prototype.deleteDocument_;
        this.query_ = FirestoreRead.prototype.query_;
        // The authentication token used for accessing Firestore
        this.accessToken = accessToken;
        this.basePath = `projects/${projectId}/databases/(default)/documents/`;
        this.baseUrl = `https://firestore.googleapis.com/${apiVersion}/${this.basePath}`;
    }
    get authToken() {
        return this.accessToken;
    }
    /**
     * Get a document.
     *
     * @param {string} path the path to the document
     * @return {object} the document object
     */
    getDocument(path) {
        const request = new Request(this.baseUrl, this.authToken);
        return this.getDocument_(path, request);
    }
    /**
     * Get a list of all documents in a collection.
     *
     * @param {string} path the path to the collection
     * @param {array} ids [Optional] String array of document names to filter. Missing documents will not be included.
     * @return {object} an array of the documents in the collection
     */
    getDocuments(path, ids) {
        let docs;
        if (!ids) {
            docs = this.query(path).Execute();
        }
        else {
            const request = new Request(this.baseUrl.replace('/documents/', '/documents:batchGet/'), this.authToken);
            docs = this.getDocuments_(this.basePath + path, request, ids);
        }
        return docs;
    }
    /**
     * Get a list of all IDs of the documents in a path
     *
     * @param {string} path the path to the collection
     * @return {object} an array of IDs of the documents in the collection
     */
    getDocumentIds(path) {
        const request = new Request(this.baseUrl, this.authToken);
        return this.getDocumentIds_(path, request);
    }
    /**
     * Create a document with the given fields and an auto-generated ID.
     *
     * @param {string} path the path where the document will be written
     * @param {object} fields the document's fields
     * @return {object} the Document object written to Firestore
     */
    createDocument(path, fields) {
        const request = new Request(this.baseUrl, this.authToken);
        return this.createDocument_(path, fields || {}, request);
    }
    /**
     * Update/patch a document at the given path with new fields.
     *
     * @param {string} path the path of the document to update. If document name not provided, a random ID will be generated.
     * @param {object} fields the document's new fields
     * @param {boolean} mask if true, the update will use a mask
     * @return {object} the Document object written to Firestore
     */
    updateDocument(path, fields, mask = false) {
        const request = new Request(this.baseUrl, this.authToken);
        return this.updateDocument_(path, fields, request, mask);
    }
    /**
     * Delete the Firestore document at the given path.
     * Note: this deletes ONLY this document, and not any subcollections.
     *
     * @param {string} path the path to the document to delete
     * @return {object} the JSON response from the DELETE request
     */
    deleteDocument(path) {
        const request = new Request(this.baseUrl, this.authToken);
        return this.deleteDocument_(path, request);
    }
    /**
     * Run a query against the Firestore Database and return an all the documents that match the query.
     * Must call .Execute() to send the request.
     *
     * @param {string} path to query
     * @return {object} the JSON response from the GET request
     */
    query(path) {
        const request = new Request(this.baseUrl, this.authToken);
        return this.query_(path, request);
    }

    /**
     * Get a batch object that can be used to perform multiple write operations as a single atomic transaction.
     *
     * @return {Batch} a batch object.
     */
    batch() {
        return new Batch(
            this.baseUrl.replace('/documents/', '/documents:batchWrite/'),
            this.authToken,
            this.basePath
        );
    }
}
/**
 * Get an object that acts as an authenticated interface with a Firestore project.
 *
 * @param {string} accessToken The access token for authentication
 * @param {string} projectId the Firestore project ID
 * @param {string} apiVersion [Optional] The Firestore API Version ("v1beta1", "v1beta2", or "v1")
 * @return {Firestore} an authenticated interface with a Firestore project (function)
 */
function getFirestore(accessToken, projectId, apiVersion = 'v1') {
    return new Firestore(accessToken, projectId, apiVersion);
}
//# sourceMappingURL=data:application/json;base64,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
