# Product Context: Custom Firestore Library

## Why this project exists

This project exists to enable secure and flexible interaction with Google Cloud Firestore from Google Apps Script, specifically addressing the challenge of organizational policies that block the creation of static service account keys. By allowing authentication via dynamically generated access tokens, it provides a robust workaround for these security restrictions.

## Problems it solves

*   **Bypasses Service Account Key Creation Restrictions:** Many organizations enforce policies that prevent the creation of downloadable JSON key files for service accounts due to security concerns. This custom library eliminates the need for such files by using short-lived access tokens.
*   **Enhances Security:** Dynamically generated, short-lived access tokens are inherently more secure than static private keys, reducing the risk of compromise.
*   **Enables Firestore Integration in Restricted Environments:** Allows Google Apps Script projects to connect to Firestore even in environments with strict security policies.
*   **Streamlines Authentication:** Provides a programmatic way to obtain and use access tokens, simplifying the authentication flow for developers.

## How it should work

The custom library should seamlessly integrate with existing Apps Script projects that need to interact with Firestore. Developers should be able to initialize the Firestore client by providing an access token and project ID, rather than a private key and client email. The underlying request mechanism should correctly use this access token for authorization.

The main project (`chat space summarizer`) will be responsible for:
1.  Obtaining a service account access token using the `getServiceAccountToken` function.
2.  Passing this token to the custom `FirestoreApp.getFirestore` method.
3.  Performing Firestore operations (e.g., `createDocument`, `updateDocument`, `deleteDocument`, `batch` operations) using the authenticated Firestore client.

## User experience goals

The user experience for developers using this library should be:
*   **Secure:** Developers can be confident that their Firestore interactions are secure and compliant with organizational policies.
*   **Efficient:** The process of obtaining and using access tokens should be straightforward and not add significant overhead to development.
*   **Reliable:** The custom library should provide reliable connectivity to Firestore, with clear error handling for token generation or Firestore operations.
*   **Maintainable:** The modifications to the library should be clear and well-documented, making it easy to understand and update in the future.
