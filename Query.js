// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
/**
 * @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery#Operator_1 FieldFilter Operator}
 */
var FieldFilterOps_;
(function (FieldFilterOps_) {
    FieldFilterOps_["=="] = "EQUAL";
    FieldFilterOps_["==="] = "EQUAL";
    FieldFilterOps_["<"] = "LESS_THAN";
    FieldFilterOps_["<="] = "LESS_THAN_OR_EQUAL";
    FieldFilterOps_[">"] = "GREATER_THAN";
    FieldFilterOps_[">="] = "GREATER_THAN_OR_EQUAL";
    FieldFilterOps_["contains"] = "ARRAY_CONTAINS";
    FieldFilterOps_["containsany"] = "ARRAY_CONTAINS_ANY";
    FieldFilterOps_["in"] = "IN";
})(FieldFilterOps_ || (FieldFilterOps_ = {}));
/**
 * @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery#Operator_2 UnaryFilter Operator}
 */
var UnaryFilterOps_;
(function (UnaryFilterOps_) {
    UnaryFilterOps_["nan"] = "IS_NAN";
    UnaryFilterOps_["null"] = "IS_NULL";
})(UnaryFilterOps_ || (UnaryFilterOps_ = {}));
/**
 * An internal object that acts as a Structured Query to be prepared before execution.
 * Chain methods to update query. Must call .execute to send request.
 *
 * @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery Firestore Structured Query}
 */
class Query {
    /**
     * @param {string} from the base collection to query
     * @param {QueryCallback} callback the function that is executed with the internally compiled query
     */
    constructor(from, callback) {
        this.callback = callback;
        if (from) {
            this.from = [{ collectionId: from }];
        }
    }
    // @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery#FieldReference Field Reference}
    fieldRef_(field) {
        const escapedField = field
            .split('.')
            .map((f) => '`' + f.replace('`', '\\`') + '`')
            .join('.');
        return { fieldPath: escapedField };
    }
    // @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery#FieldFilter Field Filter}
    fieldFilter_(field, operator, value) {
        this.validateFieldFilter_(operator);
        return {
            field: this.fieldRef_(field),
            op: FieldFilterOps_[operator],
            value: Document.wrapValue(value),
        };
    }
    // @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery#UnaryFilter Unary Filter}
    unaryFilter_(operator, field) {
        this.validateUnaryFilter_(operator);
        return {
            field: this.fieldRef_(field),
            op: UnaryFilterOps_[operator],
        };
    }
    // @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery#order Order}
    order_(field, dir) {
        const isDesc = !(dir && (dir.substr(0, 3).toUpperCase() === 'DEC' || dir.substr(0, 4).toUpperCase() === 'DESC'));
        const direction = isDesc ? 'ASCENDING' : 'DESCENDING';
        return {
            direction: direction,
            field: this.fieldRef_(field),
        };
    }
    validateFieldFilter_(val) {
        if (!(val in FieldFilterOps_)) {
            throw new Error(`Operator '${val}' not within ${Object.keys(FieldFilterOps_)}`);
        }
        return true;
    }
    validateUnaryFilter_(val) {
        if (!(val in UnaryFilterOps_)) {
            throw new Error(`Operator '${val}' not within ${Object.keys(UnaryFilterOps_)}`);
        }
        return true;
    }
    filter_(field, operator, value) {
        if (typeof operator === 'string') {
            operator = operator.toLowerCase().replace('_', '');
        }
        else if (value == null) {
            // Covers null and undefined values
            operator = 'null';
        }
        else if (Util_.isNumberNaN(value)) {
            // Covers NaN
            operator = 'nan';
        }
        if (operator in FieldFilterOps_) {
            return {
                fieldFilter: this.fieldFilter_(field, operator, value),
            };
        }
        if (operator in UnaryFilterOps_) {
            return {
                unaryFilter: this.unaryFilter_(operator, field),
            };
        }
        throw new Error('Invalid Operator given: ' + operator);
    }
    /**
     * Select Query which can narrow which fields to return.
     * Can be repeated if multiple fields are needed in the response.
     *
     * @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery#Projection Select}
     * @param {string} field The field to narrow down (if empty, returns name of document)
     * @return {this} this query object for chaining
     */
    Select(field) {
        if (!field || !field.trim()) {
            // Catch undefined or blank strings and return document name
            field = '__name__';
        }
        if (!this.select) {
            this.select = { fields: [] };
        }
        this.select['fields'].push(this.fieldRef_(field));
        return this;
    }
    /**
     * Filter Query by a given field and operator (or additionally a value).
     * Can be repeated if multiple filters required.
     * Results must satisfy all filters.
     *
     * @param {string} field The field to reference for filtering
     * @param {string} operator The operator to filter by. {@link fieldOps} {@link unaryOps}
     * @param {any} [value] Object to set the field value to. Null if using a unary operator.
     * @return {this} this query object for chaining
     */
    Where(field, operator, value) {
        if (this.where) {
            if (!this.where.compositeFilter) {
                this.where = {
                    compositeFilter: {
                        op: 'AND',
                        filters: [this.where],
                    },
                };
            }
            this.where.compositeFilter.filters.push(this.filter_(field, operator, value));
        }
        else {
            this.where = this.filter_(field, operator, value);
        }
        return this;
    }
    /**
     * Orders the Query results based on a field and specific direction.
     * Can be repeated if additional ordering is needed.
     *
     * @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1/StructuredQuery#Projection Select}
     * @param {string} field The field to order by.
     * @param {string} dir The direction to order the field by. Should be one of "asc" or "desc". Defaults to Ascending.
     * @return {this} this query object for chaining
     */
    OrderBy(field, dir) {
        if (!this.orderBy) {
            this.orderBy = [];
        }
        this.orderBy.push(this.order_(field, dir));
        return this;
    }
    /**
     * Offsets the Query results by a given number of documents.
     *
     * @param {number} offset Number of results to skip
     * @return {this} this query object for chaining
     */
    Offset(offset) {
        if (!Util_.isNumeric(offset)) {
            throw new TypeError('Offset is not a valid number!');
        }
        else if (offset < 0) {
            throw new RangeError('Offset must be >= 0!');
        }
        this.offset = offset;
        return this;
    }
    /**
     * Limits the amount Query results returned.
     *
     * @param {number} limit Number of results limit
     * @return {this} this query object for chaining
     */
    Limit(limit) {
        if (!Util_.isNumeric(limit)) {
            throw new TypeError('Limit is not a valid number!');
        }
        else if (limit < 0) {
            throw new RangeError('Limit must be >= 0!');
        }
        this.limit = limit;
        return this;
    }
    /**
     * Sets the range of Query results returned.
     *
     * @param {number} start Start result number (inclusive)
     * @param {number} end End result number (inclusive)
     * @return {this} this query object for chaining
     */
    Range(start, end) {
        if (!Util_.isNumeric(start)) {
            throw new TypeError('Range start is not a valid number!');
        }
        else if (!Util_.isNumeric(end)) {
            throw new TypeError('Range end is not a valid number!');
        }
        else if (start < 0) {
            throw new RangeError('Range start must be >= 0!');
        }
        else if (end < 0) {
            throw new RangeError('Range end must be >= 0!');
        }
        else if (start >= end) {
            throw new RangeError('Range start must be less than range end!');
        }
        this.offset = start;
        this.limit = end - start;
        return this;
    }
    /**
     * Executes the query with the given callback method and the generated query.
     * Must be used at the end of any query for execution.
     *
     * @return {Document[]} The query results from the execution
     */
    Execute() {
        return this.callback(this); // Not using callback.bind due to debugging limitations of GAS
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibW9kdWxlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsibW9kdWxlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFHQTs7R0FFRztBQUNILElBQUssZUFVSjtBQVZELFdBQUssZUFBZTtJQUNsQiwrQkFBYyxDQUFBO0lBQ2QsZ0NBQWUsQ0FBQTtJQUNmLGtDQUFpQixDQUFBO0lBQ2pCLDRDQUEyQixDQUFBO0lBQzNCLHFDQUFvQixDQUFBO0lBQ3BCLCtDQUE4QixDQUFBO0lBQzlCLDhDQUE2QixDQUFBO0lBQzdCLHFEQUFvQyxDQUFBO0lBQ3BDLDRCQUFXLENBQUE7QUFDYixDQUFDLEVBVkksZUFBZSxLQUFmLGVBQWUsUUFVbkI7QUFDRDs7R0FFRztBQUNILElBQUssZUFHSjtBQUhELFdBQUssZUFBZTtJQUNsQixpQ0FBZ0IsQ0FBQTtJQUNoQixtQ0FBa0IsQ0FBQTtBQUNwQixDQUFDLEVBSEksZUFBZSxLQUFmLGVBQWUsUUFHbkI7QUFFRDs7Ozs7R0FLRztBQUNILE1BQU0sS0FBSztJQVdUOzs7T0FHRztJQUNILFlBQVksSUFBWSxFQUFFLFFBQXVCO1FBQy9DLElBQUksQ0FBQyxRQUFRLEdBQUcsUUFBUSxDQUFDO1FBQ3pCLElBQUksSUFBSSxFQUFFO1lBQ1IsSUFBSSxDQUFDLElBQUksR0FBRyxDQUFDLEVBQUUsWUFBWSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7U0FDdEM7SUFDSCxDQUFDO0lBRUQsMkhBQTJIO0lBQzNILFNBQVMsQ0FBQyxLQUFhO1FBQ3JCLE1BQU0sWUFBWSxHQUFHLEtBQUs7YUFDdkIsS0FBSyxDQUFDLEdBQUcsQ0FBQzthQUNWLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLEtBQUssQ0FBQyxHQUFHLEdBQUcsQ0FBQzthQUM3QyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDYixPQUFPLEVBQUUsU0FBUyxFQUFFLFlBQVksRUFBRSxDQUFDO0lBQ3JDLENBQUM7SUFFRCxxSEFBcUg7SUFDckgsWUFBWSxDQUFDLEtBQWEsRUFBRSxRQUFnQixFQUFFLEtBQVU7UUFDdEQsSUFBSSxDQUFDLG9CQUFvQixDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3BDLE9BQU87WUFDTCxLQUFLLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUM7WUFDNUIsRUFBRSxFQUFHLGVBQXVCLENBQUMsUUFBUSxDQUFDO1lBQ3RDLEtBQUssRUFBRSxRQUFRLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQztTQUNqQyxDQUFDO0lBQ0osQ0FBQztJQUVELHFIQUFxSDtJQUNySCxZQUFZLENBQUMsUUFBZ0IsRUFBRSxLQUFhO1FBQzFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUNwQyxPQUFPO1lBQ0wsS0FBSyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDO1lBQzVCLEVBQUUsRUFBRyxlQUF1QixDQUFDLFFBQVEsQ0FBQztTQUN2QyxDQUFDO0lBQ0osQ0FBQztJQUVELHdHQUF3RztJQUN4RyxNQUFNLENBQUMsS0FBYSxFQUFFLEdBQVk7UUFDaEMsTUFBTSxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxLQUFLLEtBQUssSUFBSSxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxXQUFXLEVBQUUsS0FBSyxNQUFNLENBQUMsQ0FBQyxDQUFDO1FBQ2pILE1BQU0sU0FBUyxHQUFXLE1BQU0sQ0FBQyxDQUFDLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxZQUFZLENBQUM7UUFDOUQsT0FBTztZQUNMLFNBQVMsRUFBRSxTQUFTO1lBQ3BCLEtBQUssRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQztTQUM3QixDQUFDO0lBQ0osQ0FBQztJQUVELG9CQUFvQixDQUFDLEdBQVc7UUFDOUIsSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLGVBQWUsQ0FBQyxFQUFFO1lBQzdCLE1BQU0sSUFBSSxLQUFLLENBQUMsYUFBYSxHQUFHLGdCQUFnQixNQUFNLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUMsQ0FBQztTQUNqRjtRQUNELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztJQUNELG9CQUFvQixDQUFDLEdBQVc7UUFDOUIsSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLGVBQWUsQ0FBQyxFQUFFO1lBQzdCLE1BQU0sSUFBSSxLQUFLLENBQUMsYUFBYSxHQUFHLGdCQUFnQixNQUFNLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUMsQ0FBQztTQUNqRjtRQUNELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztJQUVELE9BQU8sQ0FBQyxLQUFhLEVBQUUsUUFBZ0MsRUFBRSxLQUFVO1FBQ2pFLElBQUksT0FBTyxRQUFRLEtBQUssUUFBUSxFQUFFO1lBQ2hDLFFBQVEsR0FBRyxRQUFRLENBQUMsV0FBVyxFQUFFLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFLENBQWEsQ0FBQztTQUNoRTthQUFNLElBQUksS0FBSyxJQUFJLElBQUksRUFBRTtZQUN4QixtQ0FBbUM7WUFDbkMsUUFBUSxHQUFHLE1BQU0sQ0FBQztTQUNuQjthQUFNLElBQUksS0FBSyxDQUFDLFdBQVcsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUNuQyxhQUFhO1lBQ2IsUUFBUSxHQUFHLEtBQUssQ0FBQztTQUNsQjtRQUVELElBQUssUUFBbUIsSUFBSSxlQUFlLEVBQUU7WUFDM0MsT0FBTztnQkFDTCxXQUFXLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLEVBQUUsUUFBa0IsRUFBRSxLQUFLLENBQUM7YUFDakUsQ0FBQztTQUNIO1FBRUQsSUFBSyxRQUFtQixJQUFJLGVBQWUsRUFBRTtZQUMzQyxPQUFPO2dCQUNMLFdBQVcsRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLFFBQWtCLEVBQUUsS0FBSyxDQUFDO2FBQzFELENBQUM7U0FDSDtRQUNELE1BQU0sSUFBSSxLQUFLLENBQUMsMEJBQTBCLEdBQUcsUUFBUSxDQUFDLENBQUM7SUFDekQsQ0FBQztJQUVEOzs7Ozs7O09BT0c7SUFDSCxNQUFNLENBQUMsS0FBYztRQUNuQixJQUFJLENBQUMsS0FBSyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxFQUFFO1lBQzNCLDREQUE0RDtZQUM1RCxLQUFLLEdBQUcsVUFBVSxDQUFDO1NBQ3BCO1FBQ0QsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDaEIsSUFBSSxDQUFDLE1BQU0sR0FBRyxFQUFFLE1BQU0sRUFBRSxFQUFFLEVBQUUsQ0FBQztTQUM5QjtRQUNELElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFFLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztRQUNuRCxPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRDs7Ozs7Ozs7O09BU0c7SUFDSCxLQUFLLENBQUMsS0FBYSxFQUFFLFFBQWdDLEVBQUUsS0FBVztRQUNoRSxJQUFJLElBQUksQ0FBQyxLQUFLLEVBQUU7WUFDZCxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxlQUFlLEVBQUU7Z0JBQy9CLElBQUksQ0FBQyxLQUFLLEdBQUc7b0JBQ1gsZUFBZSxFQUFFO3dCQUNmLEVBQUUsRUFBRSxLQUFLO3dCQUNULE9BQU8sRUFBRSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUM7cUJBQ3RCO2lCQUNGLENBQUM7YUFDSDtZQUNELElBQUksQ0FBQyxLQUFLLENBQUMsZUFBZ0IsQ0FBQyxPQUFRLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxFQUFFLFFBQVEsRUFBRSxLQUFLLENBQUMsQ0FBQyxDQUFDO1NBQ2pGO2FBQU07WUFDTCxJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxFQUFFLFFBQVEsRUFBRSxLQUFLLENBQUMsQ0FBQztTQUNuRDtRQUNELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztJQUVEOzs7Ozs7OztPQVFHO0lBQ0gsT0FBTyxDQUFDLEtBQWEsRUFBRSxHQUFZO1FBQ2pDLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFO1lBQ2pCLElBQUksQ0FBQyxPQUFPLEdBQUcsRUFBRSxDQUFDO1NBQ25CO1FBRUQsSUFBSSxDQUFDLE9BQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQztRQUM1QyxPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRDs7Ozs7T0FLRztJQUNILE1BQU0sQ0FBQyxNQUFjO1FBQ25CLElBQUksQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxFQUFFO1lBQzVCLE1BQU0sSUFBSSxTQUFTLENBQUMsK0JBQStCLENBQUMsQ0FBQztTQUN0RDthQUFNLElBQUksTUFBTSxHQUFHLENBQUMsRUFBRTtZQUNyQixNQUFNLElBQUksVUFBVSxDQUFDLHNCQUFzQixDQUFDLENBQUM7U0FDOUM7UUFDRCxJQUFJLENBQUMsTUFBTSxHQUFHLE1BQU0sQ0FBQztRQUNyQixPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRDs7Ozs7T0FLRztJQUNILEtBQUssQ0FBQyxLQUFhO1FBQ2pCLElBQUksQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQzNCLE1BQU0sSUFBSSxTQUFTLENBQUMsOEJBQThCLENBQUMsQ0FBQztTQUNyRDthQUFNLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRTtZQUNwQixNQUFNLElBQUksVUFBVSxDQUFDLHFCQUFxQixDQUFDLENBQUM7U0FDN0M7UUFDRCxJQUFJLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQztRQUNuQixPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRDs7Ozs7O09BTUc7SUFDSCxLQUFLLENBQUMsS0FBYSxFQUFFLEdBQVc7UUFDOUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDM0IsTUFBTSxJQUFJLFNBQVMsQ0FBQyxvQ0FBb0MsQ0FBQyxDQUFDO1NBQzNEO2FBQU0sSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLEVBQUU7WUFDaEMsTUFBTSxJQUFJLFNBQVMsQ0FBQyxrQ0FBa0MsQ0FBQyxDQUFDO1NBQ3pEO2FBQU0sSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFO1lBQ3BCLE1BQU0sSUFBSSxVQUFVLENBQUMsMkJBQTJCLENBQUMsQ0FBQztTQUNuRDthQUFNLElBQUksR0FBRyxHQUFHLENBQUMsRUFBRTtZQUNsQixNQUFNLElBQUksVUFBVSxDQUFDLHlCQUF5QixDQUFDLENBQUM7U0FDakQ7YUFBTSxJQUFJLEtBQUssSUFBSSxHQUFHLEVBQUU7WUFDdkIsTUFBTSxJQUFJLFVBQVUsQ0FBQywwQ0FBMEMsQ0FBQyxDQUFDO1NBQ2xFO1FBRUQsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7UUFDcEIsSUFBSSxDQUFDLEtBQUssR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFDO1FBQ3pCLE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0gsT0FBTztRQUNMLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLDhEQUE4RDtJQUM1RixDQUFDO0NBQ0YifQ==