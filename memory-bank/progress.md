# Progress: Custom Firestore Library

## What works

*   The `memory-bank` directory has been successfully created.
*   The core memory bank files (`projectbrief.md`, `productContext.md`, `systemPatterns.md`, `techContext.md`, `activeContext.md`) have been initialized with relevant project information.

## What's left to build

The primary remaining tasks for this project involve the actual modification and deployment of the custom Firestore library, and then updating the main `chat space summarizer` project to use it.

Specifically:

1.  **Modify Library Source Code:**
    *   Update `Firestore.js` to accept `accessToken` in `getFirestore`.
    *   Update `Request.js` to use `accessToken` for authorization headers.
    *   Ensure `Auth.js` is removed or bypassed.
    *   Verify `Firestore.js` includes `batch()` and `Batch.js` is correctly implemented.
2.  **Deploy Custom Library:**
    *   Push the modified library using `clasp`.
    *   Obtain the new Script ID.
3.  **Update Main Project (`chat space summarizer`):**
    *   Modify `appsscript.json` with required OAuth scopes.
    *   Remove old library and add new custom library.
    *   Implement `getServiceAccountToken` function.
    *   Update `saveChunksToFirestore` to use the new authentication flow.

## Current status

The project is in the initial documentation phase. The foundational memory bank is set up, providing a clear roadmap and context for the upcoming development work. No code modifications to the actual Firestore library or the main project have been made yet.

## Known issues

*   None identified at this stage, as core development has not yet begun. Potential issues may arise during library modification or integration with the main project, particularly concerning Apps Script's execution context and OAuth flows.

## Evolution of project decisions

*   The decision to use dynamic access tokens was made to comply with organizational security policies that restrict static service account keys. This is a fundamental shift from the original library's authentication method.
*   The use of a dedicated `memory-bank` directory is a strategic decision to ensure comprehensive and persistent documentation, addressing the "memory reset" characteristic of the development environment.
