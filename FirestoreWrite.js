// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
/**
 * Extends Firestore class with private method
 */
class FirestoreWrite {
    /**
     * Create a document with the given ID and fields.
     *
     * @param {string} path the path where the document will be written
     * @param {object} fields the document's fields
     * @param {string} request the Firestore Request object to manipulate
     * @return {object} the Document object written to Firestore
     */
    createDocument_(path, fields, request) {
        const pathDoc = Util_.getDocumentFromPath(path);
        const documentId = pathDoc[1];
        // Use UpdateDocument to create documents that may use special characters
        if (documentId) {
            request.addParam('currentDocument.exists', 'false');
            return this.updateDocument_(path, fields, request);
        }
        const firestoreObject = new Document(fields);
        const newDoc = request.post(pathDoc[0], firestoreObject);
        return new Document(newDoc, {});
    }
    /**
     * Update/patch a document at the given path with new fields.
     *
     * @param {string} path the path of the document to update
     * @param {object} fields the document's new fields
     * @param {string} request the Firestore Request object to manipulate
     * @param {boolean} mask if true, the update will use a mask. i.e. true: updates only specific fields, false: overwrites document with specified fields
     * @return {object} the Document object written to Firestore
     */
    updateDocument_(path, fields, request, mask = false) {
        if (mask) {
            // abort request if fields object is empty
            if (!Object.keys(fields).length) {
                throw new Error('Missing fields in Mask!');
            }
            for (const field in fields) {
                request.addParam('updateMask.fieldPaths', `\`${field.replace(/`/g, '\\`')}\``);
            }
        }
        const firestoreObject = new Document(fields);
        const updatedDoc = request.patch(path, firestoreObject);
        return new Document(updatedDoc, {});
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibW9kdWxlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsibW9kdWxlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7R0FFRztBQUNILE1BQU0sY0FBYztJQUNsQjs7Ozs7OztPQU9HO0lBQ0gsZUFBZSxDQUFDLElBQVksRUFBRSxNQUEyQixFQUFFLE9BQWdCO1FBQ3pFLE1BQU0sT0FBTyxHQUFHLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNoRCxNQUFNLFVBQVUsR0FBRyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFFOUIseUVBQXlFO1FBQ3pFLElBQUksVUFBVSxFQUFFO1lBQ2QsT0FBTyxDQUFDLFFBQVEsQ0FBQyx3QkFBd0IsRUFBRSxPQUFPLENBQUMsQ0FBQztZQUNwRCxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxFQUFFLE1BQU0sRUFBRSxPQUFPLENBQUMsQ0FBQztTQUNwRDtRQUNELE1BQU0sZUFBZSxHQUFHLElBQUksUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzdDLE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQXdCLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxlQUFlLENBQUMsQ0FBQztRQUNoRixPQUFPLElBQUksUUFBUSxDQUFDLE1BQU0sRUFBRSxFQUFjLENBQUMsQ0FBQztJQUM5QyxDQUFDO0lBRUQ7Ozs7Ozs7O09BUUc7SUFDSCxlQUFlLENBQUMsSUFBWSxFQUFFLE1BQTJCLEVBQUUsT0FBZ0IsRUFBRSxJQUFJLEdBQUcsS0FBSztRQUN2RixJQUFJLElBQUksRUFBRTtZQUNSLDBDQUEwQztZQUMxQyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxNQUFNLEVBQUU7Z0JBQy9CLE1BQU0sSUFBSSxLQUFLLENBQUMseUJBQXlCLENBQUMsQ0FBQzthQUM1QztZQUNELEtBQUssTUFBTSxLQUFLLElBQUksTUFBTSxFQUFFO2dCQUMxQixPQUFPLENBQUMsUUFBUSxDQUFDLHVCQUF1QixFQUFFLEtBQUssS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDO2FBQ2hGO1NBQ0Y7UUFFRCxNQUFNLGVBQWUsR0FBRyxJQUFJLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUM3QyxNQUFNLFVBQVUsR0FBRyxPQUFPLENBQUMsS0FBSyxDQUF3QixJQUFJLEVBQUUsZUFBZSxDQUFDLENBQUM7UUFDL0UsT0FBTyxJQUFJLFFBQVEsQ0FBQyxVQUFVLEVBQUUsRUFBYyxDQUFDLENBQUM7SUFDbEQsQ0FBQztDQUNGIn0=