// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
/**
 * Extends Firestore class with private method
 */
class FirestoreRead {
    /**
     * Get the Firestore document or collection at a given path.
     * If the collection contains enough IDs to return a paginated result, this method only returns the first page.
     *
     * @param {string} path the path to the document or collection to get
     * @param {string} request the Firestore Request object to manipulate
     * @return {object} the JSON response from the GET request
     */
    get_(path, request) {
        return this.getPage_(path, null, request);
    }
    /**
     * Get a page of results from the given path.
     * If null pageToken is supplied, returns first page.
     *`
     * @param {string} path the path to the document or collection to get
     * @param {string} pageToken if defined, is utilized for retrieving subsequent pages
     * @param {string} request the Firestore Request object to manipulate
     * @return {object} the JSON response from the GET request
     */
    getPage_(path, pageToken, request) {
        if (pageToken) {
            request.addParam('pageToken', pageToken);
        }
        return request.get(path);
    }
    /**
     * Get a list of the JSON responses received for getting documents from a collection.
     * The items returned by this function are formatted as Firestore documents (with types).
     *
     * @param {string} path the path to the collection
     * @param {string} request the Firestore Request object to manipulate
     * @return {object} an array of Firestore document objects
     */
    getDocumentResponsesFromCollection_(path, request) {
        const documents = [];
        let pageToken = null;
        let pageResponse;
        do {
            pageResponse = this.getPage_(path, pageToken, request.clone());
            pageToken = pageResponse.nextPageToken;
            if (pageResponse.documents) {
                Array.prototype.push.apply(documents, pageResponse.documents);
            }
        } while (pageToken); // Get all pages of results if there are multiple
        return documents;
    }
    /**
     * Get a list of all IDs of the documents in a collection.
     * Works with nested collections.
     *
     * @param {string} path the path to the collection
     * @param {string} request the Firestore Request object to manipulate
     * @return {object} an array of IDs of the documents in the collection
     */
    getDocumentIds_(path, request) {
        const documents = this.query_(path, request).Select().Execute();
        return Util_.stripBasePath(path, documents);
    }
    /**
     * Get a document.
     *
     * @param {string} path the path to the document
     * @param {string} request the Firestore Request object to manipulate
     * @return {object} an object maexpping the document's fields to their values
     */
    getDocument_(path, request) {
        const doc = request.get(path);
        if (!doc.fields) {
            throw new Error('No document with `fields` found at path ' + path);
        }
        return new Document(doc, {});
    }
    /**
     * Get documents with given IDs.
     *
     * @see {@link https://firebase.google.com/docs/firestore/reference/rest/v1beta1/projects.databases.documents/batchGet Firestore Documents BatchGet}
     * @param {string} path the path to the document
     * @param {string} request the Firestore Request object to manipulate
     * @param {array} ids String array of document names
     * @return {object} an object mapping the document's fields to their values
     */
    getDocuments_(path, request, ids) {
        // Format to absolute paths (relative to API endpoint)
        const idPaths = ids.map((doc) => path + '/' + doc);
        const payload = { documents: idPaths };
        let documents = request.post('', payload);
        // Remove missing entries
        documents = documents.filter((docItem) => docItem.found);
        return documents.map((docItem) => {
            const doc = new Document(docItem.found, { readTime: docItem.readTime });
            doc.readTime = docItem.readTime;
            return doc;
        });
    }
    /**
     * Set up a Query to receive data from a collection
     *
     * @param {string} path the path to the document or collection to query
     * @param {string} request the Firestore Request object to manipulate
     * @return {object} A FirestoreQuery object to set up the query and eventually execute
     */
    query_(path, request) {
        const grouped = Util_.getCollectionFromPath(path);
        request.route('runQuery');
        const callback = (query) => {
            // Send request to innermost document with given query
            const payload = { structuredQuery: query };
            const responseObj = request.post(grouped[0], payload);
            // Filter out results without documents and unwrap document fields
            const documents = responseObj.reduce((docs, docItem) => {
                if (docItem.document) {
                    const doc = new Document(docItem.document, { readTime: docItem.readTime });
                    docs.push(doc);
                }
                return docs;
            }, []);
            return documents;
        };
        return new Query(grouped[1], callback);
    }
}
//# sourceMappingURL=data:application/json;base64,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