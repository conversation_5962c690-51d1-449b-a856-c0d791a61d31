# Custom Firestore Library API Documentation

This document provides API documentation for the custom version of the `grahamearley/FirestoreGoogleAppsScript` library, modified to accept a dynamically generated access token.

## Introduction

This custom library allows secure interaction with Google Cloud Firestore from Google Apps Script by using short-lived access tokens generated via service account impersonation, bypassing the need for static private key files.

## Installation and Setup

To use this custom library, you must first deploy it to your Google Apps Script environment and then configure your main project to use it.

### 1. Deploy the Custom Library

1.  **Modify the Library Source Code:**
    *   Ensure `Firestore.js` and `Request.js` are modified as per the `project_requirements.md` to accept an `accessToken` directly.
    *   The `Auth.js` file should be removed as it's no longer needed.
    *   Ensure `Firestore.js` includes the `batch()` method.
    *   Ensure `Batch.js` is created.
2.  **Push your modified code to Apps Script:**
    ```bash
    clasp push
    ```
3.  **Get the new Library ID:**
    *   Go to your "Custom Firestore Library" project in the Apps Script editor.
    *   Go to "Project Settings" (gear icon).
    *   Copy the "Script ID". This is your new custom library ID.

### 2. Update Your Main Project (`chat space summarizer`)

1.  **Update `appsscript.json` in your main project:**
    Add the necessary OAuth scopes:
    ```json
    {
      "oauthScopes": [
        "https://www.googleapis.com/auth/iam",
        "https://www.googleapis.com/auth/script.external_request",
        "https://www.googleapis.com/auth/datastore"
      ]
    }
    ```
2.  **Add your custom `FirestoreApp` library:**
    *   In your main Apps Script project, go to "Libraries".
    *   Add your new custom library using the Script ID: `1WyoVOxSyrRQALgOzTFD_0XLiGfMhoV06rGqQGHKd8Ljen9o99mvxVa91`. Give it the identifier `FirestoreApp`.

## Usage

### `FirestoreApp.getFirestore(accessToken, projectId, apiVersion)`

Initializes an authenticated interface to a Firestore project.

*   **`accessToken`** (`string`, required): The short-lived access token obtained via service account impersonation.
*   **`projectId`** (`string`, required): Your Google Cloud Project ID where Firestore is enabled.
*   **`apiVersion`** (`string`, optional, default: `'v1'`): The Firestore API Version (e.g., `'v1beta1'`, `'v1beta2'`, or `'v1'`).

**Returns:** `Firestore` - An authenticated interface to interact with your Firestore database.

### `Firestore.batch()`

Returns a `Batch` object that can be used to perform multiple write operations (create, update, delete) as a single atomic transaction.

**Returns:** `Batch` - A batch object.

### `Batch.createDocument(path, fields)`

Adds a document creation operation to the batch.

*   **`path`** (`string`, required): The path where the document will be written.
*   **`fields`** (`object`, required): The document's fields.

**Returns:** `Batch` - This batch object for chaining.

### `Batch.updateDocument(path, fields, mask)`

Adds a document update operation to the batch.

*   **`path`** (`string`, required): The path of the document to update.
*   **`fields`** (`object`, required): The document's new fields.
*   **`mask`** (`boolean`, optional, default: `false`): If `true`, the update will use a mask (updates only specific fields); otherwise, it overwrites the document with specified fields.

**Returns:** `Batch` - This batch object for chaining.

### `Batch.deleteDocument(path)`

Adds a document deletion operation to the batch.

*   **`path`** (`string`, required): The path to the document to delete.

**Returns:** `Batch` - This batch object for chaining.

### `Batch.commit()`

Commits all operations added to the batch as a single atomic transaction.

**Returns:** `object` - The JSON response from the commit request.

### `getServiceAccountToken(serviceAccountEmail, scopes)`

Fetches a short-lived access token for a service account via impersonation. This function should be added to your main Apps Script project's `Code.js` (or similar) file.

*   **`serviceAccountEmail`** (`string`, required): The email of the service account to impersonate (e.g., `<EMAIL>`).
*   **`scopes`** (`Array<string>`, required): An array of OAuth scopes for the token (e.g., `['https://www.googleapis.com/auth/datastore']` or `['https://www.googleapis.com/auth/cloud-platform']`).

**Returns:** `string` - The generated access token.

**Throws:** `Error` - If token fetching fails.

### Example Usage in Your Main Project (`Code.js`)

```javascript
/**
 * Fetches a short-lived access token for a service account via impersonation.
 * Requires 'https://www.googleapis.com/auth/iam' and 'https://www.googleapis.com/auth/script.external_request' scopes.
 * @param {string} serviceAccountEmail The email of the service account to impersonate.
 * @param {Array<string>} scopes An array of OAuth scopes for the token (e.g., ['https://www.googleapis.com/auth/datastore']).
 * @returns {string} The access token.
 * @throws {Error} If token fetching fails.
 */
function getServiceAccountToken(serviceAccountEmail, scopes) {
  const url = `https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/${serviceAccountEmail}:generateAccessToken`;

  const payload = {
    scope: scopes,
    lifetime: '3600s' // Token will be valid for 1 hour
  };

  const options = {
    method: 'POST',
    contentType: 'application/json',
    headers: {
      'Authorization': 'Bearer ' + ScriptApp.getOAuthToken() // Authorize with user's token
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true // Important to catch errors in try/catch
  };

  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    if (responseCode >= 200 && responseCode < 300) {
      const token = JSON.parse(responseText).accessToken;
      return token;
    } else {
      throw new Error(`API Error: ${responseCode} - ${responseText}`);
    }
  } catch (error) {
    console.error(`Error fetching token: ${error.message}`, error.stack);
    throw new Error(`Failed to get token: ${error.message}`);
  }
}

// Example of how to use the custom FirestoreApp in your main project
function saveChunksToFirestore(chunks) {
  const scriptProperties = PropertiesService.getScriptProperties();
  const projectId = scriptProperties.getProperty('firestore_project_id');

  if (!projectId) {
    const errorMsg = "Firestore project ID not found in Script Properties. Please set 'firestore_project_id'.";
    console.error(errorMsg);
    return { successful: 0, failed: chunks.length, errors: [{ message: errorMsg, chunksAttempted: chunks.length }] };
  }

  // Replace with your service account email
  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    console.error("Failed to get service account token:", e.message);
    return { successful: 0, failed: chunks.length, errors: [{ message: `Failed to get service account token: ${e.message}`, chunksAttempted: chunks.length }] };
  }

  // Initialize FirestoreApp with the access token
  const firestore = FirestoreApp.getFirestore(accessToken, projectId);

  // Example of batching multiple writes
  const batch = firestore.batch();
  let successfulWrites = 0;
  const errors = [];

  for (const chunk of chunks) {
    try {
      // Assuming each chunk has a 'path' and 'fields' property
      batch.createDocument(chunk.path, chunk.fields);
      successfulWrites++;
    } catch (e) {
      errors.push({ message: `Failed to add chunk to batch: ${e.message}`, chunk });
    }
  }

  try {
    batch.commit();
    console.log(`Successfully saved ${successfulWrites} chunks to Firestore.`);
  } catch (e) {
    errors.push({ message: `Failed to commit batch: ${e.message}`, chunksAttempted: successfulWrites });
    console.error("Batch commit failed:", e.message);
  }

  return { successful: successfulWrites, failed: chunks.length - successfulWrites, errors };
}
