// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
class Util_ {
    /**
     * RegEx test for root path references. Groups relative path for extraction.
     */
    static get regexPath() {
        return /^projects\/.+?\/databases\/\(default\)\/documents\/(.+\/.+)$/;
    }
    /**
     * RegEx test for testing for binary data by checking for non-printable characters.
     * Parsing strings for binary data is completely dependent on the data being sent over.
     */
    static get regexBinary() {
        // eslint-disable-next-line no-control-regex
        return /[\x00-\x08\x0E-\x1F]/;
    }
    /**
     * RegEx test for finding and capturing milliseconds.
     * Apps Scripts doesn't support RFC3339 Date Formats, nanosecond precision must be trimmed.
     */
    static get regexDatePrecision() {
        return /(\.\d{3})\d+/;
    }
    /**
     * Checks if a number is an integer.
     *
     * @param {value} n value to check
     * @return {boolean} true if value can be coerced into an integer, false otherwise
     */
    static isInt(n) {
        return n % 1 === 0;
    }
    /**
     * Check if a value is a valid number.
     *
     * @param {value} val value to check
     * @return {boolean} true if a valid number, false otherwise
     */
    static isNumeric(val) {
        return Number(parseFloat(val.toString())) === val;
    }
    /**
     * Check if a value is of type Number but is NaN.
     * This check prevents seeing non-numeric values as NaN.
     *
     * @param {value} value value to check
     * @return {boolean} true if NaN, false otherwise
     */
    static isNumberNaN(value) {
        return typeof value === 'number' && isNaN(value);
    }
    /**
     * Gets collection of documents with the given path
     *
     * @param {string} path Collection path
     * @return {array} Collection of documents
     */
    static getCollectionFromPath(path) {
        return this.getColDocFromPath(path, false);
    }
    /**
     * Gets document with the given path
     *
     * @param {string} path Document path
     * @return {object} Document object
     */
    static getDocumentFromPath(path) {
        return this.getColDocFromPath(path, true);
    }
    /**
     * Gets collection or document with the given path
     *
     * @param {string} path Document/Collection path
     * @return {array|object} Collection of documents or a single document
     */
    static getColDocFromPath(path, isDocument) {
        // Path defaults to empty string if it doesn't exist. Remove insignificant slashes.
        const splitPath = (path || '').split('/').filter(function (p) {
            return p;
        });
        const len = splitPath.length;
        this.cleanParts(splitPath);
        // Set item path to document if isDocument, otherwise set to collection if exists.
        // This works because path is always in the format of "collection/document/collection/document/etc.."
        const item = len && (len & 1) ^ isDocument ? splitPath.splice(len - 1, 1)[0] : '';
        // Remainder of path is in splitPath. Put back together and return.
        return [splitPath.join('/'), item];
    }
    /**
     * Gets document names from list of documents
     *
     * @param {string} path Relative collection path
     * @param {FirestoreAPI.Document[]} docs Array of documents
     * @return {string[]} of names
     */
    static stripBasePath(path, docs) {
        return docs.map((doc) => {
            const ref = doc.name.match(Util_.regexPath)[1]; // Gets the doc name field and extracts the relative path
            return ref.substr(path.length + 1); // Skip over the given path to gain the ID values
        });
    }
    /**
     * Validates Collection and Document names
     *
     * @see {@link https://firebase.google.com/docs/firestore/quotas#collections_documents_and_fields Firestore Limits}
     * @param {array} parts Array of strings representing document path
     * @return {array} of URI Encoded path names
     * @throws {Error} Validation errors if it doesn't meet API guidelines
     */
    static cleanParts(parts) {
        return parts.map(function (part, i) {
            const type = i & 1 ? 'Collection' : 'Document';
            if (part === '.' || part === '..') {
                throw new TypeError(type + ' name cannot solely consist of a single period (.) or double periods (..)');
            }
            if (part.indexOf('__') === 0 && part.endsWith('__')) {
                throw new TypeError(type + ' name cannot be a dunder name (begin and end with double underscores)');
            }
            return encodeURIComponent(part);
        });
    }
    /**
     * Splits up path to be cleaned
     *
     * @param {string} path to be cleaned
     * @return {string} path that is URL-safe
     */
    static cleanPath(path) {
        return this.cleanParts(path.split('/')).join('/');
    }
    static parameterize(obj, encode = true) {
        const process = encode ? encodeURI : (s) => s;
        return Object.entries(obj)
            .map(([k, v]) => `${process(k)}=${process(v)}`)
            .join('&');
    }
}
//# sourceMappingURL=data:application/json;base64,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