# Technical Context: Custom Firestore Library

## Technologies Used

*   **Google Apps Script (GAS):** The primary development environment and runtime for both the custom Firestore library and the consuming `chat space summarizer` project.
*   **Google Cloud Firestore:** The NoSQL document database that the library interacts with.
*   **Google Cloud IAM Credentials API:** Used by the main project to generate short-lived access tokens for service account impersonation.
*   **`UrlFetchApp` (GAS Service):** Used for making HTTP requests to Google APIs, both for token generation and Firestore interactions.
*   **`clasp` (Command Line Apps Script Project):** Used for pushing local code changes to Google Apps Script projects.
*   **JavaScript (ES5/ES6+):** The programming language used for Apps Script development.
*   **JSON:** Used for API request/response payloads.

## Development Setup

*   **Local Development Environment:** A local machine with `clasp` installed and configured for the Google Apps Script projects.
*   **Google Cloud Project:** A GCP project with Firestore enabled and a service account configured with appropriate IAM roles (e.g., `roles/datastore.user` for Firestore access, `roles/iam.serviceAccountTokenCreator` for token generation).
*   **Apps Script Editor:** Used for managing project settings, libraries, and deploying scripts.

## Technical Constraints

*   **Google Apps Script Limitations:**
    *   Execution time limits (e.g., 6 minutes for most scripts).
    *   Memory limits.
    *   Limited debugging capabilities compared to traditional environments.
    *   Specific OAuth scope requirements for various Google services.
    *   The need to manage library versions and updates manually.
*   **Organizational Policies:** The primary constraint is the policy blocking service account key creation, which necessitates the dynamic token approach.
*   **API Rate Limits:** Awareness of Google API rate limits for both IAM Credentials API and Firestore API is necessary for robust applications.
*   **Token Lifetime:** Access tokens generated via `generateAccessToken` have a limited lifetime (e.g., 1 hour), requiring the application to refresh them as needed.

## Dependencies

*   **`grahamearley/FirestoreGoogleAppsScript`:** The original third-party library that this project forks and modifies.
*   **Google Cloud SDK / `gcloud` CLI:** While not a direct code dependency, it's essential for local authentication (`gcloud auth application-default login`) for development purposes.
*   **`ScriptApp.getOAuthToken()`:** Used in the `getServiceAccountToken` function to authorize the call to the IAM Credentials API with the user's own token.

## Tool Usage Patterns

*   **`clasp push`:** Frequently used to deploy local code changes to the Apps Script environment.
*   **Apps Script Editor:** Used for configuring project settings, linking to GCP projects, managing libraries, and running one-off setup functions.
*   **Google Cloud Console:** Used for managing GCP projects, service accounts, IAM permissions, and monitoring Cloud Logging.
*   **VS Code:** The primary IDE for code development and modification.
