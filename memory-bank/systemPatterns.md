# System Patterns: Custom Firestore Library

## System Architecture

The system involves two main components:
1.  **Custom Firestore Library (this project):** A modified version of `grahamearley/FirestoreGoogleAppsScript` deployed as a standalone Google Apps Script library. It acts as an intermediary, handling the low-level HTTP requests to the Firestore API.
2.  **Main Apps Script Project (`chat space summarizer`):** The consuming application that utilizes the custom Firestore library. It is responsible for generating the access token and orchestrating Firestore operations.

## Key Technical Decisions

*   **Authentication Mechanism:** Transition from static private key authentication to dynamic, short-lived access token authentication via service account impersonation. This is a critical decision driven by organizational security policies.
*   **Library Modification:** Direct modification of the `Firestore.js` and `Request.js` files within the forked library to accommodate the new authentication flow.
*   **Service Account Impersonation:** Leveraging Google Cloud IAM Credentials API (`iamcredentials.googleapis.com`) to generate access tokens on behalf of a service account. This requires the main project to have the `https://www.googleapis.com/auth/iam` scope and the service account to have appropriate roles (e.g., `roles/iam.serviceAccountTokenCreator`).

## Design Patterns in Use

*   **Dependency Injection (Implicit):** The `accessToken` is "injected" into the `FirestoreApp.getFirestore` function, allowing the `Firestore` instance to be configured with the necessary authentication credential without hardcoding it internally.
*   **Wrapper/Adapter Pattern:** The custom Firestore library acts as a wrapper or adapter around the native Firestore REST API, simplifying interactions for Apps Script developers.
*   **Factory Pattern (for `FirestoreApp.getFirestore`):** The `getFirestore` function serves as a factory method, responsible for creating and returning a configured `Firestore` object.
*   **Builder Pattern (for `Batch` operations):** The `Batch` object, with its `createDocument`, `updateDocument`, and `deleteDocument` methods, follows a builder pattern, allowing multiple operations to be chained before a final `commit`.

## Component Relationships

*   **Main Project -> Custom Firestore Library:** The main project depends on the custom library for Firestore interactions. It calls `FirestoreApp.getFirestore` and then uses the returned `Firestore` object.
*   **Main Project -> Google Cloud IAM Credentials API:** The main project directly calls the IAM Credentials API to obtain the `accessToken`.
*   **Custom Firestore Library -> Google Cloud Firestore REST API:** The custom library makes direct HTTP requests to the Firestore REST API, using the provided `accessToken` for authorization.

## Critical Implementation Paths

1.  **Access Token Generation:** The `getServiceAccountToken` function in the main project is crucial. Its correct implementation, including OAuth scopes and error handling, is vital for the entire system to function.
2.  **Firestore Client Initialization:** The `FirestoreApp.getFirestore` function in the custom library must correctly receive and utilize the `accessToken` to set up the underlying request mechanism.
3.  **Request Authorization:** The `Request.js` file within the custom library must correctly attach the `Authorization: Bearer <token>` header to all outgoing Firestore API requests.
4.  **Batch Operations:** The `Batch.js` and `batch()` method in `Firestore.js` must correctly aggregate and commit multiple write operations atomically.
