// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
/**
 * Manages the requests to send. Chain methods to update options.
 * Must call .get/.post/.patch/.remove to send the request with given options.
 */
class Request {
    /**
     * @param url the base url to utilize
     * @param authToken authorization token to make requests
     * @param options [Optional] set of options to utilize over the default headers
     */
    constructor(url, authToken, options) {
        this.url = url;
        this.queryString = '';
        this.authToken = authToken || '';
        // Set default header options if none are passed in
        this.options = options || {
            headers: {
                'content-type': 'application/json',
                'Authorization': 'Bearer ' + this.authToken,
            },
        };
        this.options['muteHttpExceptions'] = true;
    }
    /**
     * Sets the payload option
     *
     * @param obj Object payload to stringify
     * @return {Request} this request to be chained
     */
    payload(obj) {
        this.options['payload'] = JSON.stringify(obj);
        return this;
    }
    /**
     * Sets the type of REST method to send
     *
     * @param type String value equal to one of the REST method types
     * @param path the path to send the request to
     * @return {any} this request to be chained
     */
    method_(type, path) {
        const url = this.url + Util_.cleanPath(path || '') + this.queryString;
        this.options['method'] = type;
        const response = UrlFetchApp.fetch(url, this.options);
        const responseObj = JSON.parse(response.getContentText());
        this.checkForError(responseObj);
        return responseObj;
    }
    /**
     * Adds a parameter to the URL query string.
     * Can be repeated for additional key-value mappings
     *
     * @param {string} key the key to add
     * @param {string} value the value to set
     * @return {this} this request to be chained
     */
    addParam(key, value) {
        this.queryString += (this.queryString.startsWith('?') ? '&' : '?') + Util_.parameterize({ [key]: value });
        return this;
    }
    /**
     * Alters the route by prepending the query string.
     *
     * @param {string} route to set
     * @return {this} this request to be chained
     */
    route(route) {
        this.queryString = `:${route}${this.queryString}`;
        return this;
    }
    /**
     * Set request as a GET method
     *
     * @param {string} path the path to send the request to
     * @return {T} JSON Object response
     */
    get(path) {
        return this.method_('get', path);
    }
    /**
     * Set request as a POST method
     *
     * @param path the path to send the request to
     * @param obj [Optional] object to send as payload
     * @return {Request} this request to be chained
     */
    post(path, obj) {
        if (obj) {
            this.payload(obj);
        }
        return this.method_('post', path);
    }
    /**
     * Set request as a PATCH method.
     *
     * @param path the path to send the request to
     * @param obj Optional object to send as payload
     * @return {Request} this request to be chained
     */
    patch(path, obj) {
        if (obj) {
            this.payload(obj);
        }
        return this.method_('patch', path);
    }
    /**
     * Set request as a DELETE method (delete is a keyword)
     *
     * @param path the path to send the request to
     * @return {Request} this request to be chained
     */
    remove(path) {
        return this.method_('delete', path);
    }
    /**
     * Used to clone the request instance. Useful for firing multiple requests.
     *
     * @return {Request} A copy of this object
     */
    clone() {
        return new Request(this.url, this.authToken, this.options);
    }
    /**
     * Validate response object for errors
     *
     * @param {any} responseObj HTTP response object to validate
     * @throws Error if HTTP request errors found
     */
    checkForError(responseObj) {
        if (responseObj.error) {
            throw new Error(responseObj.error.message || responseObj.error.error_description || responseObj.error);
        }
        if (Array.isArray(responseObj) && responseObj.length && responseObj[0].error) {
            throw new Error(responseObj[0].error.message);
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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
