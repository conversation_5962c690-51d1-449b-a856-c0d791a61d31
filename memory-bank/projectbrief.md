# Project Brief: Custom Firestore Library for Google Apps Script

## Core Requirements and Goals

The primary goal of this project is to create and deploy a custom version of the `grahamearley/FirestoreGoogleAppsScript` library. This custom library will be modified to accept a dynamically generated access token (via service account impersonation) for authentication with Google Cloud Firestore, thereby bypassing organizational policies that restrict the creation of static service account keys.

Additionally, the project involves updating a main Google Apps Script project, `chat space summarizer`, to utilize this newly deployed custom Firestore library.

## Key Objectives

1.  **Modify Library Authentication:**
    *   Adjust `Firestore.js` to accept an `accessToken` directly in its `getFirestore` function.
    *   Modify `Request.js` to use this `accessToken` for constructing the `Authorization: Bearer <token>` header in `UrlFetchApp.fetch` calls.
    *   Remove or bypass `Auth.js` as it will no longer be needed for JWT generation from private keys.
    *   Ensure `Firestore.js` includes the `batch()` method and `Batch.js` is created for batch operations.
2.  **Deploy Custom Library:**
    *   Push the modified library code to Google Apps Script using `clasp`.
    *   Obtain the new Script ID for the deployed custom library.
3.  **Update Main Project (`chat space summarizer`):**
    *   Add necessary OAuth scopes (`https://www.googleapis.com/auth/iam`, `https://www.googleapis.com/auth/script.external_request`, `https://www.googleapis.com/auth/datastore`) to `appsscript.json`.
    *   Remove the old `FirestoreApp` library and add the new custom library using its Script ID, ensuring it's identified as `FirestoreApp`.
    *   Implement the `getServiceAccountToken` function in `Code.js` to fetch short-lived access tokens via service account impersonation.
    *   Modify existing Firestore interaction functions (e.g., `saveChunksToFirestore`) to use the `accessToken` when initializing `FirestoreApp.getFirestore`.

## Important Considerations

*   This project involves maintaining a forked version of a third-party library, requiring manual updates if the original library changes.
*   The solution must adhere to Google Cloud Platform and Firebase authentication best practices, particularly regarding keyless authentication for local development and service identities for production.
