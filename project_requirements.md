# Custom Firestore Library Setup for Google Apps Script

This guide provides instructions to create and deploy a custom version of the `grahamearley/FirestoreGoogleAppsScript` library. This custom version will be modified to accept a dynamically generated access token (via service account impersonation) instead of a static private key, allowing you to bypass organization policies that block service account key creation.

**Important:** This process involves maintaining a forked version of a third-party library. You will be responsible for keeping it updated if the original library receives important changes.

## Step 1-3: Modify the Library Source Code to Accept Access Token

This is the most critical step. You will modify the library's authentication mechanism. The goal is to change how `FirestoreApp.getFirestore` is initialized so it can accept an `accessToken` directly.

**Files to Focus On:**
*   `Auth.js`: This file is responsible for generating the JWT from the private key. We will bypass this.
*   `Firestore.js`: This file contains the `getFirestore` method.
*   `Request.js`: This file likely handles the actual `UrlFetchApp` calls and adds the `Authorization` header.

**Conceptual Changes:**

1.  **Modify `Firestore.js`'s `getFirestore` function:**
    *   Change its signature to accept an `accessToken` instead of `privateKey` and `clientEmail`.
    *   Example (conceptual, you'll need to adapt to the actual code):

    ```typescript
    // In Firestore.js (conceptual modification)
    // Original:
    // function getFirestore(clientEmail: string, privateKey: string, projectId: string): Firestore { ... }

    // Modified:
    function getFirestore(accessToken: string, projectId: string): Firestore {
        // Instead of creating a new Auth instance with privateKey,
        // you'll need to pass the accessToken down to where requests are made.
        // This might involve modifying the constructor of the Firestore class
        // or a Request class it uses.
        // Example: return new Firestore(new Auth(accessToken), projectId);
        // Or, if Auth is internal, pass accessToken directly to Request.
        return new Firestore(new Request(accessToken), projectId); // Assuming Request can take a token
    }
    ```

2.  **Modify `Request.js` to use the `accessToken`:**
    *   Locate where `UrlFetchApp.fetch` is called and where the `Authorization` header is constructed.
    *   Ensure that the `accessToken` is used directly in the `Authorization: Bearer <token>` header.
    *   Example (conceptual, look for `UrlFetchApp.fetch` calls):

    ```typescript
    // In Request.js (conceptual modification)
    // Look for a method like `execute` or `send` that makes the actual HTTP call.
    // It likely has a parameter for `options` or constructs them internally.

    // Example of how the Authorization header might be set:
    // const headers = {
    //     'Authorization': 'Bearer ' + this.auth.getToken(), // Original
    //     'Content-Type': 'application/json'
    // };

    // Modified to use a direct token:
    // If your Request class constructor now takes an accessToken:
    // constructor(private accessToken: string) { ... }
    // ...
    // const headers = {
    //     'Authorization': 'Bearer ' + this.accessToken, // Use the passed token
    //     'Content-Type': 'application/json'
    // };
    ```

    **Note:** This step requires careful examination of the library's internal structure. You'll need to trace how the `privateKey` is currently used to generate an authorization header and replace that logic with the direct use of the `accessToken`.

## Step 4: Deploy Your Custom Library

1.  **Push your modified code to Apps Script:**
    ```bash
    clasp push
    ```
2.  **Get the new Library ID:**
    *   Go back to your "Custom Firestore Library" project in the Apps Script editor.
    *   Go to "Project Settings" (gear icon).
    *   Copy the "Script ID". This is your new custom library ID.

## Step 5: Update Your Main Project (`chat space summarizer`)

Now, you will update your main project to use this new custom library and the service account impersonation method.

1.  **Update `appsscript.json` in your main project:**
    *   Add the necessary OAuth scopes for service account impersonation and Firestore.

    ```json
    {
      "oauthScopes": [
        "https://www.googleapis.com/auth/iam",
        "https://www.googleapis.com/auth/script.external_request",
        "https://www.googleapis.com/auth/datastore"
      ]
    }
    ```

2.  **Remove the old `FirestoreApp` library and add your custom one:**
    *   In your main `chat space summarizer` Apps Script project, go to "Libraries".
    *   Remove the old `FirestoreApp` library (ID `1VUSl4b1r1eoNcRWotZM3e87ygkxvXltOgyDZhixqncz9lQ3MjfT1iKFw`).
    *   Add your new custom library using the Script ID you obtained in Step 4. Give it the identifier `FirestoreApp` (or whatever you prefer, but `FirestoreApp` keeps consistency).

3.  **Modify `Code.js` in your main project:**
    *   **Add the `getServiceAccountToken` function:**

    ```javascript
    /**
     * Fetches a short-lived access token for a service account via impersonation.
     * Requires 'https://www.googleapis.com/auth/iam' and 'https://www.googleapis.com/auth/script.external_request' scopes.
     * @param {string} serviceAccountEmail The email of the service account to impersonate.
     * @param {Array<string>} scopes An array of OAuth scopes for the token (e.g., ['https://www.googleapis.com/auth/datastore']).
     * @returns {string} The access token.
     * @throws {Error} If token fetching fails.
     */
    function getServiceAccountToken(serviceAccountEmail, scopes) {
      const url = `https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/${serviceAccountEmail}:generateAccessToken`;

      const payload = {
        scope: scopes,
        lifetime: '3600s' // Token will be valid for 1 hour
      };

      const options = {
        method: 'POST',
        contentType: 'application/json',
        headers: {
          'Authorization': 'Bearer ' + ScriptApp.getOAuthToken() // Authorize with user's token
        },
        payload: JSON.stringify(payload),
        muteHttpExceptions: true // Important to catch errors in try/catch
      };

      try {
        const response = UrlFetchApp.fetch(url, options);
        const responseCode = response.getResponseCode();
        const responseText = response.getContentText();

        if (responseCode >= 200 && responseCode < 300) {
          const token = JSON.parse(responseText).accessToken;
          return token;
        } else {
          throw new Error(`API Error: ${responseCode} - ${responseText}`);
        }
      } catch (error) {
        console.error(`Error fetching token: ${error.message}`, error.stack);
        throw new Error(`Failed to get token: ${error.message}`);
      }
    }
    ```

    *   **Modify `saveChunksToFirestore`:**
        *   Remove the `PropertiesService` calls for `private_key` and `client_email`.
        *   Call `getServiceAccountToken` to get the `accessToken`.
        *   Initialize `FirestoreApp` using the new signature you defined in your custom library (e.g., `FirestoreApp.getFirestore(accessToken, projectId)`).

    ```javascript
    // In Code.js (conceptual modification to saveChunksToFirestore)
    function saveChunksToFirestore(chunks) {
      const scriptProperties = PropertiesService.getScriptProperties();
      const projectId = scriptProperties.getProperty('firestore_project_id'); // Still need project ID

      if (!projectId) {
        const errorMsg = "Firestore project ID not found in Script Properties. Please set 'firestore_project_id'.";
        console.error(errorMsg);
        return { successful: 0, failed: chunks.length, errors: [{ message: errorMsg, chunksAttempted: chunks.length }] };
      }

      // Replace with your service account email
      const serviceAccountEmail = '<EMAIL>';
      const firestoreScopes = ['https://www.googleapis.com/auth/datastore']; // Or 'https://www.googleapis.com/auth/cloud-platform' for broader access

      let accessToken;
      try {
        accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
      } catch (e) {
        console.error("Failed to get service account token:", e.message);
        return { successful: 0, failed: chunks.length, errors: [{ message: `Failed to get service account token: ${e.message}`, chunksAttempted: chunks.length }] };
      }

      // Initialize FirestoreApp with the access token (assuming your custom library supports this)
      const firestore = FirestoreApp.getFirestore(accessToken, projectId);

      // ... rest of your batch saving logic remains similar ...
      // (The batch.createDocument and batch.commit calls should work if the library is correctly modified)
    }
