// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
function StoreCredentials_() {
    /** DO NOT SAVE CREDENTIALS HERE */
    const email = '<EMAIL>';
    const key = '-----B<PERSON>IN PRIVATE KEY-----\nLine\nLine\n-----END PRIVATE KEY-----';
    const projectId = 'xxx';
    PropertiesService.getUserProperties().setProperties({
        email: email,
        key: key,
        project: projectId,
    });
}
class Tests {
    constructor(email, key, projectId, apiVersion = 'v1', clearCollection = false) {
        this.pass = [];
        this.fail = new Map();
        let funcs = Object.getOwnPropertyNames(Tests.prototype).filter((property) => typeof this[property] === 'function' && property !== 'constructor');
        /** Test Initializer */
        try {
            this.db = getFirestore(email, key, projectId, apiVersion);
            this.pass.push('Test_Get_Firestore');
        }
        catch (e) {
            // On failure, fail the remaining tests without execution
            this.fail.set('Test_Get_Firestore', e);
            const err = new Error('Test Initialization Error');
            err.stack = 'See Test_Get_Firestore Error';
            for (const func of funcs) {
                this.fail.set(func, err);
            }
            return;
        }
        this.expected_ = {
            'array value': ['string123', 42, false, { 'nested map property': 123 }],
            'number value': 100,
            'string value 이': 'The fox jumps over the lazy dog 름',
            'boolean value': true,
            'map value (nested object)': {
                foo: 'bar',
            },
            'null value': null,
            'timestamp value': new Date(),
            'geopoint value': {
                latitude: 29.9792,
                longitude: 31.1342,
            },
            'reference value': this.db.basePath + 'Test Collection/New Document',
        };
        /** Only run test to remove residual Test Documents **/
        if (clearCollection) {
            funcs = ['Test_Delete_Documents'];
        }
        /** Test all methods in this class */
        for (const func of funcs) {
            try {
                this[func]();
                this.pass.push(func);
            }
            catch (e) {
                if (typeof e === 'string') {
                    const err = new Error('AssertionError');
                    err.stack = e;
                    // eslint-disable-next-line no-ex-assign
                    e = err;
                }
                this.fail.set(func, e);
            }
        }
    }
    /**
     * All Test methods should not take any parameters, and return nothing.
     * Tests should throw exceptions to fail.
     * Can leverage {@link https://sites.google.com/site/scriptsexamples/custom-methods/gsunit GSUnit}.
     */
    Test_Create_Document_Bare() {
        const path = 'Test Collection';
        const newDoc = this.db.createDocument(path);
        GSUnit.assertNotUndefined(newDoc);
        GSUnit.assertNotUndefined(newDoc.name);
        GSUnit.assertTrue(newDoc.name.startsWith(this.db.basePath + path + '/'));
        GSUnit.assertNotUndefined(newDoc.createTime);
        GSUnit.assertNotUndefined(newDoc.updateTime);
        GSUnit.assertEquals(newDoc.createTime, newDoc.updateTime);
        GSUnit.assertRoughlyEquals(+new Date(), +newDoc.created, 1000);
    }
    Test_Create_Document_Name() {
        const path = 'Test Collection/New Document';
        const newDoc = this.db.createDocument(path);
        GSUnit.assertNotUndefined(newDoc);
        GSUnit.assertEquals(path, newDoc.path);
        GSUnit.assertEquals(this.db.basePath + path, newDoc.name);
        GSUnit.assertNotUndefined(newDoc.createTime);
        GSUnit.assertNotUndefined(newDoc.updateTime);
        GSUnit.assertRoughlyEquals(+new Date(), +newDoc.updated, 1000);
    }
    Test_Create_Document_Name_Duplicate() {
        const path = 'Test Collection/New Document';
        try {
            this.db.createDocument(path);
            GSUnit.fail('Duplicate document without error');
        }
        catch (e) {
            if (e.message !== `Document already exists: ${this.db.basePath}${path}`) {
                throw e;
            }
        }
    }
    Test_Create_Document_Data() {
        const path = 'Test Collection/New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ';
        const newDoc = this.db.createDocument(path, this.expected_);
        GSUnit.assertEquals(path, newDoc.path);
        GSUnit.assertObjectEquals(this.expected_, newDoc.obj);
    }
    Test_Update_Document_Overwrite_Default() {
        const original = {
            'number value': -100,
            'string value 이': 'The fox jumps over the lazy dog 름',
        };
        const path = 'Test Collection/Updatable Document Default';
        this.db.createDocument(path, original);
        const expected = {
            'string value 이': 'Qwerty निर्वाण',
            'null value': 'Not a Null',
        };
        const updatedDoc = this.db.updateDocument(path, expected);
        GSUnit.assertEquals(path, updatedDoc.path);
        GSUnit.assertObjectEquals(expected, updatedDoc.obj);
    }
    Test_Update_Document_Overwrite() {
        const original = {
            'number value': 10,
            'string value 이': 'The fox jumps over the lazy dog 름',
        };
        const path = 'Test Collection/Updatable Document Overwrite';
        this.db.createDocument(path, original);
        const expected = { 'number value': 42 };
        const updatedDoc = this.db.updateDocument(path, expected, false);
        GSUnit.assertEquals(path, updatedDoc.path);
        GSUnit.assertObjectEquals(expected, updatedDoc.obj);
    }
    Test_Update_Document_Mask() {
        const expected = {
            'number value': 1234567890,
            'string value 이': 'The fox jumps over the lazy dog 름',
        };
        const path = 'Test Collection/Updatable Document Mask';
        this.db.createDocument(path, expected);
        const updater = { 'string value 이': 'The new wave `~' };
        const updatedDoc = this.db.updateDocument(path, updater, true);
        Object.assign(expected, updater);
        GSUnit.assertEquals(path, updatedDoc.path);
        GSUnit.assertObjectEquals(expected, updatedDoc.obj);
    }
    Test_Update_Document_Overwrite_Missing() {
        const path = 'Test Collection/Missing Document Overwrite';
        const expected = { 'boolean value': false };
        const updatedDoc = this.db.updateDocument(path, expected, false);
        GSUnit.assertEquals(path, updatedDoc.path);
        GSUnit.assertObjectEquals(expected, updatedDoc.obj);
    }
    Test_Update_Document_Mask_Missing() {
        const path = 'Test Collection/Missing Document Mask';
        const expected = { 'boolean value': true };
        const updatedDoc = this.db.updateDocument(path, expected, true);
        GSUnit.assertEquals(path, updatedDoc.path);
        GSUnit.assertObjectEquals(expected, updatedDoc.obj);
    }
    Test_Get_Document() {
        const path = 'Test Collection/New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ';
        const doc = this.db.getDocument(path);
        GSUnit.assertEquals(path, doc.path);
        GSUnit.assertObjectEquals(this.expected_, doc.obj);
    }
    Test_Get_Document_Missing() {
        const path = 'Test Collection/Missing Document';
        try {
            this.db.getDocument(path);
            GSUnit.fail('Missing document without error');
        }
        catch (e) {
            if (e.message !== `Document "${this.db.basePath}${path}" not found.`) {
                throw e;
            }
        }
    }
    Test_Get_Documents() {
        const path = 'Test Collection';
        const docs = this.db.getDocuments(path);
        GSUnit.assertEquals(8, docs.length);
        const doc = docs.find((doc) => doc.name.endsWith('/New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ'));
        GSUnit.assertNotUndefined(doc);
        GSUnit.assertObjectEquals(this.expected_, doc.obj);
    }
    Test_Get_Documents_By_ID() {
        const path = 'Test Collection';
        const ids = [
            'New Document',
            'Updatable Document Default',
            'Updatable Document Overwrite',
            'Updatable Document Mask',
            'Missing Document',
        ];
        const docs = this.db.getDocuments(path, ids);
        GSUnit.assertEquals(ids.length - 1, docs.length);
    }
    Test_Get_Documents_By_ID_Missing() {
        const path = 'Missing Collection';
        const ids = [
            'New Document',
            'Updatable Document Default',
            'Updatable Document Overwrite',
            'Updatable Document Mask',
            'Missing Document',
        ];
        const docs = this.db.getDocuments(path, ids);
        GSUnit.assertEquals(0, docs.length);
    }
    Test_Get_Documents_By_ID_Empty() {
        const path = 'Test Collection';
        const ids = [];
        const docs = this.db.getDocuments(path, ids);
        GSUnit.assertEquals(0, docs.length);
    }
    Test_Get_Document_IDs() {
        const path = 'Test Collection';
        const docs = this.db.getDocumentIds(path);
        GSUnit.assertEquals(8, docs.length);
    }
    Test_Get_Document_IDs_Missing() {
        const path = 'Missing Collection';
        const docs = this.db.getDocumentIds(path);
        GSUnit.assertEquals(0, docs.length);
    }
    Test_Query_All() {
        const path = 'Test Collection';
        const expected = this.db.getDocuments(path);
        expected.forEach((doc) => {
            delete doc.readTime;
        });
        const docs = this.db.query(path).Execute();
        docs.forEach((doc) => {
            delete doc.readTime;
        });
        GSUnit.assertArrayEquals(expected, docs);
    }
    Test_Query_Select_Name() {
        const path = 'Test Collection';
        const docs = this.db.query(path).Select().Execute();
        GSUnit.assertEquals(8, docs.length);
    }
    Test_Query_Select_Name_Number() {
        const path = 'Test Collection';
        const docs = this.db.query(path).Select().Select('number value').Execute();
        GSUnit.assertEquals(8, docs.length);
    }
    Test_Query_Select_String() {
        const path = 'Test Collection';
        const docs = this.db.query(path).Select('string value 이').Execute();
        GSUnit.assertEquals(8, docs.length);
    }
    Test_Query_Where_EqEq_String() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('string value 이', '==', 'The fox jumps over the lazy dog 름').Execute();
        GSUnit.assertEquals(1, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_EqEqEq_String() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('string value 이', '===', 'The fox jumps over the lazy dog 름').Execute();
        GSUnit.assertEquals(1, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_Eq_Number() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('number value', '==', 100).Execute();
        GSUnit.assertEquals(1, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_Lt_Number() {
        const expected = ['Updatable Document Overwrite'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('number value', '<', 100).Execute();
        GSUnit.assertEquals(1, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_Lte_Number() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ', 'Updatable Document Overwrite'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('number value', '<=', 100).Execute();
        GSUnit.assertEquals(2, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_Gt_Number() {
        const expected = ['Updatable Document Mask'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('number value', '>', 100).Execute();
        GSUnit.assertEquals(1, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_Gte_Number() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ', 'Updatable Document Mask'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('number value', '>=', 100).Execute();
        GSUnit.assertEquals(2, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_Contains() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('array value', 'contains', 42).Execute();
        GSUnit.assertEquals(1, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_Contains_Any() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('array value', 'containsany', [false, 0, 42, 'bar']).Execute();
        GSUnit.assertEquals(1, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_In() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ', 'Updatable Document Overwrite'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('number value', 'in', [0, 100, 42]).Execute();
        GSUnit.assertEquals(2, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
    }
    Test_Query_Where_Nan() {
        // Unable to store NaN values to Firestore, so no results
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('number value', NaN).Execute();
        GSUnit.assertEquals(0, docs.length);
    }
    Test_Query_Where_Null() {
        const expected = ['New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ'];
        const path = 'Test Collection';
        const docs = this.db.query(path).Where('null value', null).Execute();
        GSUnit.assertEquals(1, docs.length);
        GSUnit.assertArrayEqualsIgnoringOrder(expected.map((p) => `${path}/${p}`), docs.map((doc) => doc.path));
        GSUnit.assertObjectEquals(this.expected_, docs[0].obj);
    }
    Test_Query_OrderBy_Number() {
        const expected = [
            'Updatable Document Overwrite',
            'New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ',
            'Updatable Document Mask',
        ];
        const path = 'Test Collection';
        const docs = this.db.query(path).Select().OrderBy('number value').Execute();
        GSUnit.assertArrayEquals(expected, Util_.stripBasePath(path, docs));
    }
    Test_Query_OrderBy_Number_ASC() {
        const expected = [
            'Updatable Document Overwrite',
            'New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ',
            'Updatable Document Mask',
        ];
        const path = 'Test Collection';
        const docs = this.db.query(path).Select().OrderBy('number value', 'asc').Execute();
        GSUnit.assertArrayEquals(expected, Util_.stripBasePath(path, docs));
    }
    Test_Query_OrderBy_Number_DESC() {
        const expected = [
            'Updatable Document Mask',
            'New Document !@#$%^&*(),.<>?;\':"[]{}|-=_+áéíóúæÆÑ',
            'Updatable Document Overwrite',
        ];
        const path = 'Test Collection';
        const docs = this.db.query(path).Select().OrderBy('number value', 'desc').Execute();
        GSUnit.assertArrayEquals(expected, Util_.stripBasePath(path, docs));
    }
    Test_Query_Offset() {
        const path = 'Test Collection';
        const docs = this.db.query(path).Offset(2).Execute();
        GSUnit.assertEquals(6, docs.length);
    }
    Test_Query_Limit() {
        const path = 'Test Collection';
        const docs = this.db.query(path).Limit(2).Execute();
        GSUnit.assertEquals(2, docs.length);
    }
    Test_Query_Range() {
        const path = 'Test Collection';
        const docs = this.db.query(path).Range(2, 5).Execute();
        GSUnit.assertEquals(5 - 2, docs.length);
    }
    Test_Delete_Documents() {
        const collection = 'Test Collection';
        const docs = this.db.getDocumentIds(collection).map((path) => this.db.deleteDocument(collection + '/' + path));
        docs.forEach((doc) => GSUnit.assertObjectEquals({}, doc));
    }
    Test_Delete_Document_Missing() {
        const path = 'Test Collection/Missing Document';
        const noDoc = this.db.deleteDocument(path);
        GSUnit.assertObjectEquals({}, noDoc);
    }
}
function RunTests_(cacheSeconds) {
    const scriptProps = PropertiesService.getUserProperties().getProperties();
    const tests = new Tests(scriptProps['email'], scriptProps['key'], scriptProps['project'], 'v1');
    const { pass, fail } = tests;
    for (const [func, err] of fail) {
        console.log(`Test Failed: ${func} (${err.message})\n${err.stack}`);
    }
    console.log(`Completed ${pass.length + fail.size} Tests.`);
    return {
        schemaVersion: 1,
        label: 'tests',
        message: `✔ ${pass.length}, ✘ ${Object.keys(fail).length}`,
        color: Object.keys(fail).length ? 'red' : 'green',
        cacheSeconds: cacheSeconds,
    };
}
function cacheResults_(cachedBadge) {
    /* Script owner should set up a trigger for this function to cache the test results.
     * The badge fetching these Test Results (on README) is set to cache the image after 1 hour.
     * GitHub creates anonymized URLs which timeout after 3 seconds,
     * which is longer than the time it takes to execute all the tests.
     */
    const maxCache = 3600;
    const results = JSON.stringify(RunTests_(maxCache));
    CacheService.getUserCache().put('Test Results', results, maxCache);
    // Send the min cache allowed @see {@link https://shields.io/endpoint ShieldsIO Endpoint}
    return results.replace(`"cacheSeconds":${maxCache}`, `"cacheSeconds":${cachedBadge ? maxCache : 300}`);
}
/* eslint-disable-next-line @typescript-eslint/no-unused-vars */
function clearCache() {
    /** Allow user to clear Cached Results **/
    const scriptProps = PropertiesService.getUserProperties().getProperties();
    new Tests(scriptProps['email'], scriptProps['key'], scriptProps['project'], 'v1', true);
    CacheService.getUserCache().remove('Test Results');
}
/* eslint-disable-next-line @typescript-eslint/no-unused-vars */
function doGet(evt) {
    // Sending /exec?nocache when calling to ignore the cache check
    const useCache = evt.queryString !== 'nocache';
    const results = (useCache && CacheService.getUserCache().get('Test Results')) || cacheResults_(useCache);
    return ContentService.createTextOutput(results);
}
//# sourceMappingURL=data:application/json;base64,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