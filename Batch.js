// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
/**
 * A batch object that can be used to perform multiple write operations as a single atomic transaction.
 */
class Batch {
    /**
     * @param {string} baseUrl the base url to utilize
     * @param {string} authToken authorization token to make requests
     * @param {string} basePath the base path to the Firestore project
     */
    constructor(baseUrl, authToken, basePath) {
        this.baseUrl = baseUrl;
        this.authToken = authToken;
        this.basePath = basePath;
        this.writes = [];
    }
    /**
     * Create a document with the given fields and an auto-generated ID.
     *
     * @param {string} path the path where the document will be written
     * @param {object} fields the document's fields
     * @return {Batch} this batch to be chained
     */
    createDocument(path, fields) {
        const pathDoc = Util_.getDocumentFromPath(path);
        const documentId = pathDoc[1];
        const write = {
            currentDocument: { exists: false },
            update: {
                name: `${this.basePath}${path}`,
                fields: new Document(fields).fields,
            },
        };
        this.writes.push(write);
        return this;
    }
    /**
     * Update/patch a document at the given path with new fields.
     *
     * @param {string} path the path of the document to update
     * @param {object} fields the document's new fields
     * @param {boolean} mask if true, the update will use a mask
     * @return {Batch} this batch to be chained
     */
    updateDocument(path, fields, mask = false) {
        const update = {
            update: {
                name: `${this.basePath}${path}`,
                fields: new Document(fields).fields,
            },
        };
        if (mask) {
            update.updateMask = {
                fieldPaths: Object.keys(fields).map(field => `\`${field.replace(/`/g, '\\`')}\``),
            };
        }
        this.writes.push(update);
        return this;
    }
    /**
     * Delete the Firestore document at the given path.
     * Note: this deletes ONLY this document, and not any subcollections.
     *
     * @param {string} path the path to the document to delete
     * @return {Batch} this batch to be chained
     */
    deleteDocument(path) {
        const write = {
            delete: `${this.basePath}${path}`,
        };
        this.writes.push(write);
        return this;
    }
    /**
     * Commit the changes in the batch as a single atomic transaction.
     *
     * @return {object} the JSON response from the commit request
     */
    commit() {
        const request = new Request(this.baseUrl, this.authToken);
        const payload = {
            writes: this.writes,
        };
        const response = request.post('', payload);
        this.writes = []; // Clear writes after commit
        return response;
    }
}
//# sourceMappingURL=data:application/json;base64,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
