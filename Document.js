// Compiled using ts2gas 3.6.1 (TypeScript 3.8.3)
"use strict";
/**
 * Firestore Document
 */
class Document {
    /**
     *
     *
     * @param obj
     * @param name
     */
    constructor(obj, name) {
        //Treat parameters as existing Document with extra parameters to merge in
        if (typeof name === 'object') {
            Object.assign(this, obj);
            Object.assign(this, name);
        }
        else {
            this.fields = Document.wrapMap(obj).fields;
            if (name) {
                this.name = name;
            }
        }
    }
    get created() {
        return this.createTime ? Document.unwrapDate(this.createTime) : new Date();
    }
    get updated() {
        return this.updateTime ? Document.unwrapDate(this.updateTime) : new Date();
    }
    get read() {
        return this.readTime ? Document.unwrapDate(this.readTime) : new Date();
    }
    get path() {
        var _a;
        return ((_a = this.name) === null || _a === void 0 ? void 0 : _a.match(Util_.regexPath))[1];
    }
    /**
     * Extract fields from a Firestore document.
     *
     * @param {object} firestoreDoc the Firestore document whose fields will be extracted
     * @return {object} an object with the given document's fields and values
     */
    get obj() {
        return Document.unwrapObject(this);
    }
    toString() {
        return `Document (${Util_.getDocumentFromPath(this.name)[1]})`;
    }
    static unwrapValue(obj) {
        // eslint-disable-next-line prefer-const
        let [type, val] = Object.entries(obj)[0];
        switch (type) {
            case 'referenceValue':
            case 'bytesValue':
            case 'stringValue':
            case 'booleanValue':
            case 'geoPointValue':
                return val;
            case 'doubleValue':
                return parseFloat(val);
            case 'integerValue':
                return parseInt(val);
            case 'mapValue':
                return this.unwrapObject(val);
            case 'arrayValue':
                return this.unwrapArray(val.values);
            case 'timestampValue':
                return this.unwrapDate(val);
            case 'nullValue':
            default:
                return null;
        }
    }
    static unwrapObject(obj) {
        return Object.entries(obj.fields || {}).reduce((o, [key, val]) => {
            o[key] = Document.unwrapValue(val);
            return o;
        }, {});
    }
    static unwrapArray(wrappedArray = []) {
        return wrappedArray.map(this.unwrapValue, this);
    }
    static unwrapDate(wrappedDate) {
        // Trim out extra microsecond precision
        return new Date(wrappedDate.replace(Util_.regexDatePrecision, '$1'));
    }
    static wrapValue(val) {
        const type = typeof val;
        switch (type) {
            case 'string':
                return this.wrapString(val);
            case 'object':
                return this.wrapObject(val);
            case 'number':
                return this.wrapNumber(val);
            case 'boolean':
                return this.wrapBoolean(val);
            default:
                return this.wrapNull();
        }
    }
    static wrapString(string) {
        // Test for root path reference inclusion (see Util.js)
        if (Util_.regexPath.test(string)) {
            return this.wrapRef(string);
        }
        // Test for binary data in string (see Util.js)
        if (Util_.regexBinary.test(string)) {
            return this.wrapBytes(string);
        }
        return { stringValue: string };
    }
    static wrapObject(obj) {
        if (!obj) {
            return this.wrapNull();
        }
        if (Array.isArray(obj)) {
            return this.wrapArray(obj);
        }
        // instanceof fails for code referencing this library
        if (obj instanceof Date || obj.constructor.name === 'Date') {
            return this.wrapDate(obj);
        }
        // Check if LatLng type
        if (Object.keys(obj).length === 2 && 'latitude' in obj && 'longitude' in obj) {
            return this.wrapLatLong(obj);
        }
        return { mapValue: this.wrapMap(obj) };
    }
    static wrapMap(obj) {
        return {
            fields: Object.entries(obj).reduce((o, [key, val]) => {
                o[key] = Document.wrapValue(val);
                return o;
            }, {}),
        };
    }
    static wrapNull() {
        return { nullValue: null };
    }
    static wrapBytes(bytes) {
        return { bytesValue: bytes };
    }
    static wrapRef(ref) {
        return { referenceValue: ref };
    }
    static wrapNumber(num) {
        const func = Util_.isInt(num) ? this.wrapInt : this.wrapDouble;
        return func(num);
    }
    static wrapInt(int) {
        return { integerValue: int.toString() };
    }
    static wrapDouble(double) {
        return { doubleValue: double };
    }
    static wrapBoolean(boolean) {
        return { booleanValue: boolean };
    }
    static wrapDate(date) {
        return { timestampValue: date.toISOString() };
    }
    static wrapLatLong(latLong) {
        return { geoPointValue: latLong };
    }
    static wrapArray(array) {
        const wrappedArray = array.map(this.wrapValue, this);
        return { arrayValue: { values: wrappedArray } };
    }
}
//# sourceMappingURL=data:application/json;base64,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